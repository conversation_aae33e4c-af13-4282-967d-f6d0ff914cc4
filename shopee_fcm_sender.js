#!/usr/bin/env node
/**
 * Node.js script to send FCM registration data to Shopee server
 * Replicates exact protobuf structure and TCP protocol from com.shopeepay.merchant.vn
 */

const net = require('net');
const crypto = require('crypto');

class ProtobufBuilder {
    constructor() {
        this.buffer = Buffer.alloc(0);
    }

    writeVarint(value) {
        const bytes = [];
        while (value >= 0x80) {
            bytes.push((value & 0x7F) | 0x80);
            value >>>= 7;
        }
        bytes.push(value & 0x7F);
        this.buffer = Buffer.concat([this.buffer, Buffer.from(bytes)]);
    }

    writeString(fieldNumber, value) {
        if (value) {
            const tag = (fieldNumber << 3) | 2; // Wire type 2 (length-delimited)
            this.writeVarint(tag);
            const utf8Bytes = Buffer.from(value, 'utf8');
            this.writeVarint(utf8Bytes.length);
            this.buffer = Buffer.concat([this.buffer, utf8Bytes]);
        }
    }

    writeBytes(fieldNumber, value) {
        if (value && value.length > 0) {
            const tag = (fieldNumber << 3) | 2; // Wire type 2 (length-delimited)
            this.writeVarint(tag);
            this.writeVarint(value.length);
            this.buffer = Buffer.concat([this.buffer, value]);
        }
    }

    writeInt32(fieldNumber, value) {
        if (value !== null && value !== undefined) {
            const tag = (fieldNumber << 3) | 0; // Wire type 0 (varint)
            this.writeVarint(tag);
            this.writeVarint(value);
        }
    }

    writeBool(fieldNumber, value) {
        if (value !== null && value !== undefined) {
            const tag = (fieldNumber << 3) | 0; // Wire type 0 (varint)
            this.writeVarint(tag);
            this.writeVarint(value ? 1 : 0);
        }
    }

    writeMessage(fieldNumber, messageBytes) {
        if (messageBytes && messageBytes.length > 0) {
            const tag = (fieldNumber << 3) | 2; // Wire type 2 (length-delimited)
            this.writeVarint(tag);
            this.writeVarint(messageBytes.length);
            this.buffer = Buffer.concat([this.buffer, messageBytes]);
        }
    }

    getBytes() {
        return this.buffer;
    }

    clear() {
        this.buffer = Buffer.alloc(0);
    }
}

class DeviceExtBuilder {
    buildDeviceExt(deviceIdBase64, userAgent, extInfoBytes) {
        const builder = new ProtobufBuilder();

        // Field 3: deviceid (bytes)
        const deviceIdBytes = Buffer.from(deviceIdBase64, 'base64');
        builder.writeBytes(3, deviceIdBytes);

        // Field 4: device_fingerprint (bytes) - 24 bytes from extinfo
        if (extInfoBytes && extInfoBytes.length > 0) {
            builder.writeBytes(4, extInfoBytes);
        }

        // Field 5: user_agent (string)
        builder.writeString(5, userAgent);

        // Field 9: is_rooted (bool) - false
        builder.writeBool(9, false);

        // Field 10: is_fingerprint_tempered (bool) - false
        builder.writeBool(10, false);

        return builder.getBytes();
    }
}

class SetUserInfoBuilder {
    buildSetUserInfo(requestId, country, machineCode, deviceIdBase64, language, fcmToken, userAgent, extInfoBytes, userToken = null, pnOption = null) {
        const builder = new ProtobufBuilder();

        // Field 1: requestid (string)
        builder.writeString(1, requestId);

        // Field 2: token (string) - User authentication token (for logged in users)
        if (userToken) {
            builder.writeString(2, userToken);
        }

        // Field 3: country (string)
        builder.writeString(3, country);

        // Field 5: machine_code (string)
        builder.writeString(5, machineCode);

        // Field 6: deviceid (bytes)
        const deviceIdBytes = Buffer.from(deviceIdBase64, 'base64');
        builder.writeBytes(6, deviceIdBytes);

        // Field 7: pn_option (uint64) - Push notification preferences (for logged in users)
        if (pnOption !== null) {
            builder.writeInt32(7, pnOption);
        }

        // Field 8: language (string)
        builder.writeString(8, language);

        // Field 13: pn_token (bytes) - FCM token as UTF-8 bytes
        const fcmTokenBytes = Buffer.from(fcmToken, 'utf8');
        builder.writeBytes(13, fcmTokenBytes);

        // Field 17 (0x11): appversion (int32) - 0x8534 = 34100
        builder.writeInt32(17, 34100);

        // Field 21 (0x15): ext (DeviceExt message)
        const deviceExtBuilder = new DeviceExtBuilder();
        const deviceExtBytes = deviceExtBuilder.buildDeviceExt(deviceIdBase64, userAgent, extInfoBytes);
        builder.writeMessage(21, deviceExtBytes);

        return builder.getBytes();
    }
}

class ShopeeTCPClient {
    constructor(host, port) {
        this.host = host;
        this.port = port;
        this.socket = null;
    }

    formatHexPayload(payload) {
        let hexString = "";
        for (let i = 0; i < payload.length; i++) {
            const byte = payload[i] & 0xFF;
            const hex = byte.toString(16).toUpperCase().padStart(2, '0');
            hexString += hex + " ";

            // Add line breaks every 16 bytes for readability
            if ((i + 1) % 16 === 0) {
                hexString += "\n[TCP]   ";
            }
        }
        return `[TCP]   ${hexString}`;
    }

    connect() {
        return new Promise((resolve, reject) => {
            this.socket = new net.Socket();
            this.socket.setTimeout(10000);

            this.socket.connect(this.port, this.host, () => {
                console.log(`[TCP] Connected to ${this.host}:${this.port}`);
                resolve(true);
            });

            this.socket.on('error', (err) => {
                console.log(`[TCP] Connection failed: ${err.message}`);
                reject(err);
            });

            this.socket.on('timeout', () => {
                console.log('[TCP] Connection timeout');
                reject(new Error('Connection timeout'));
            });
        });
    }

    sendPacket(command, payload) {
        return new Promise((resolve, reject) => {
            if (!this.socket) {
                reject(new Error('Not connected'));
                return;
            }

            // Construct packet: [command:2][length:3][payload]
            const packetLength = payload.length;
            const header = Buffer.alloc(5);
            header.writeUInt16BE(command, 0);           // 2 bytes command
            header.writeUIntBE(packetLength, 2, 3);     // 3 bytes length

            const packet = Buffer.concat([header, payload]);

            console.log(`[TCP] Sending packet:`);
            console.log(`[TCP]   Command: 0x${command.toString(16).padStart(4, '0')}`);
            console.log(`[TCP]   Payload size: ${packetLength} bytes`);
            console.log(`[TCP]   Total size: ${packet.length} bytes`);
            console.log(`[TCP]   Header: ${header.toString('hex')}`);

            // Print full payload in hex format like Frida capture
            console.log(`[TCP]   Full payload (hex):`);
            const hexString = this.formatHexPayload(payload);
            console.log(hexString);

            this.socket.write(packet);
            console.log('[TCP] Packet sent successfully');
            resolve(true);
        });
    }

    receiveResponse(timeout = 5000) {
        return new Promise((resolve, reject) => {
            if (!this.socket) {
                reject(new Error('Not connected'));
                return;
            }

            const timer = setTimeout(() => {
                console.log('[TCP] Receive timeout');
                resolve(null);
            }, timeout);

            this.socket.once('data', (data) => {
                clearTimeout(timer);

                if (data.length < 5) {
                    console.log(`[TCP] Incomplete header received: ${data.length} bytes`);
                    resolve(null);
                    return;
                }

                const command = data.readUInt16BE(0);
                const length = data.readUIntBE(2, 3);
                const payload = data.slice(5, 5 + length);

                console.log(`[TCP] Response received:`);
                console.log(`[TCP]   Command: 0x${command.toString(16).padStart(4, '0')}`);
                console.log(`[TCP]   Length: ${length} bytes`);
                console.log(`[TCP]   Payload: ${payload.toString('hex')}`);

                resolve({ command, payload });
            });
        });
    }

    close() {
        if (this.socket) {
            this.socket.destroy();
            this.socket = null;
            console.log('[TCP] Connection closed');
        }
    }
}

async function sendFCMRegistration() {
    // Exact data from your latest Frida capture (authenticated user)
    // Captured from com.shopeepay.merchant.vn with authenticated user ID: 1218807615
    // Two packets captured: 346 bytes (FCM registration) + 61 bytes (status/heartbeat)

    const USER_ID = 1218807615; // Captured user ID
    const FCM_TOKEN = "cdXw6jlATJ6FZdVx0fI_J-:APA91bGQEUBsis_s5QO6DFkx1yb9P1gZCHMXSnlJRq2QGhEaSqQDx1Ph5nerS6hQAsFxTTOjAFO4gzetSg_kqYNwK16WP69dn4Nph_tyvvmjCqbyr2iK89g";
    const DEVICE_ID_BASE64 = "bpp3MAqOYG5iTtEqK72Ip1sYYySecg/oNxzcnIse1ys=";
    const REQUEST_ID = "8862069515812627215";
    const USER_AGENT = "Brand/oppo Model/cph1911 OSVer/29 Manufacturer/OPPO";
    const EXT_INFO_BYTES = Buffer.from("eabf016cef4d250b_unknown", 'utf8'); // 24 bytes from capture

    // User authentication data from your capture (user ID: 1218807615)
    const USER_TOKEN = "B:EMiPQ9VoxY21WxyKIcoXnQTNzUIiCYmJRIjsc/WlxTs91GssKKSUBo7F7n5F1+/KoyklwS24EHe4a3VzAAEI+ZPl+Zqrl0FRvC9kQU9DvYVwrc4jZb5JDlG4J5V3C5LpaHvYlSpWXg==";
    const PN_OPTION = 0; // Push notification option from your capture

    // Server details (replace with actual server)
    const SERVER_HOST = "partner.api.shopee.vn";
    const SERVER_PORT = 20346;

    console.log("Shopee FCM Registration TCP Sender (Node.js)");
    console.log("=".repeat(50));
    console.log(`[AUTH] Authenticated User ID: ${USER_ID}`);
    console.log(`[AUTH] Login Status: true`);
    console.log(`[AUTH] Has Token: true`);
    console.log("");

    // Build protobuf message
    const builder = new SetUserInfoBuilder();
    const protobufPayload = builder.buildSetUserInfo(
        REQUEST_ID,
        "VN",
        "android_gcm",
        DEVICE_ID_BASE64,
        "vi",
        FCM_TOKEN,
        USER_AGENT,
        EXT_INFO_BYTES,
        null,        // Don't include user token in FCM registration (sent separately)
        PN_OPTION    // Include push notification preferences
    );

    console.log(`[Protobuf] Built SetUserInfo message (authenticated user):`);
    console.log(`[Protobuf]   Request ID: ${REQUEST_ID}`);
    console.log(`[Protobuf]   Country: VN`);
    console.log(`[Protobuf]   Machine Code: android_gcm`);
    console.log(`[Protobuf]   Device ID: ${DEVICE_ID_BASE64}`);
    console.log(`[Protobuf]   Language: vi`);
    console.log(`[Protobuf]   FCM Token: ${FCM_TOKEN.substring(0, 50)}...`);
    console.log(`[Protobuf]   User Agent: ${USER_AGENT}`);
    console.log(`[Protobuf]   User Token: ${USER_TOKEN.substring(0, 50)}...`);
    console.log(`[Protobuf]   PN Option: ${PN_OPTION}`);
    console.log(`[Protobuf]   Payload size: ${protobufPayload.length} bytes`);
    console.log(`[Protobuf]   Expected size: 346 bytes (from capture)`);

    // Send via TCP
    const client = new ShopeeTCPClient(SERVER_HOST, SERVER_PORT);

    try {
        await client.connect();

        // Compare with expected hex from Frida capture
        const expectedHex = "0A 13 38 38 36 32 30 36 39 35 31 35 38 31 32 36 32 37 32 31 35 1A 02 56 4E 2A 0B 61 6E 64 72 6F 69 64 5F 67 63 6D 32 20 6E 9A 77 30 0A 8E 60 6E 62 4E D1 2A 2B BD 88 A7 5B 18 63 24 9E 72 0F E8 37 1C DC 9C 8B 1E D7 2B 42 02 76 69 6A 8E 01 63 64 58 77 36 6A 6C 41 54 4A 36 46 5A 64 56 78 30 66 49 5F 4A 2D 3A 41 50 41 39 31 62 47 51 45 55 42 73 69 73 5F 73 35 51 4F 36 44 46 6B 78 31 79 62 39 50 31 67 5A 43 48 4D 58 53 6E 6C 4A 52 71 32 51 47 68 45 61 53 71 51 44 78 31 50 68 35 6E 65 72 53 36 68 51 41 73 46 78 54 54 4F 6A 41 46 4F 34 67 7A 65 74 53 67 5F 6B 71 59 4E 77 4B 31 36 57 50 36 39 64 6E 34 4E 70 68 5F 74 79 76 76 6D 6A 43 71 62 79 72 32 69 4B 38 39 67 88 01 B4 8A 02 AA 01 75 1A 20 6E 9A 77 30 0A 8E 60 6E 62 4E D1 2A 2B BD 88 A7 5B 18 63 24 9E 72 0F E8 37 1C DC 9C 8B 1E D7 2B 22 18 65 61 62 66 30 31 36 63 65 66 34 64 32 35 30 62 5F 75 6E 6B 6E 6F 77 6E 2A 33 42 72 61 6E 64 2F 6F 70 70 6F 20 4D 6F 64 65 6C 2F 63 70 68 31 39 31 31 20 4F 53 56 65 72 2F 32 39 20 4D 61 6E 75 66 61 63 74 75 72 65 72 2F 4F 50 50 4F 48 01 50 00";
        console.log(`\n[COMPARE] Expected hex from Frida capture (346 bytes):`);
        console.log(`[COMPARE] ${expectedHex}`);

        // Send FCM registration packet (command 0x43)
        console.log(`\n[TCP] Sending first packet (FCM registration):`);
        await client.sendPacket(0x43, protobufPayload);

        // Wait for response
        const response = await client.receiveResponse();
        if (response) {
            console.log(`[TCP] Server response received`);
            console.log(`[TCP] Response command: 0x${response.command.toString(16).padStart(4, '0')}`);
            console.log(`[TCP] Response payload: ${response.payload.toString('hex')}`);
        } else {
            console.log(`[TCP] No response received`);
        }

        // Send second packet (61 bytes) as captured in Frida log
        console.log(`\n[TCP] Sending second packet (status/heartbeat):`);
        const secondPacketHex = "0A 13 38 38 36 32 30 36 39 35 31 35 38 31 32 36 32 37 32 31 35 10 00 62 24 82 01 00 82 02 1E 6A 04 10 01 68 02 D0 02 80 CE 8A B0 06 A0 03 01 B0 03 01 BA 03 08 42 55 53 49 4E 45 53 53";
        const secondPacketBytes = Buffer.from(secondPacketHex.replace(/ /g, ''), 'hex');
        await client.sendPacket(0x43, secondPacketBytes);

        // Wait for second response
        const response2 = await client.receiveResponse();
        if (response2) {
            console.log(`[TCP] Second response received`);
            console.log(`[TCP] Response command: 0x${response2.command.toString(16).padStart(4, '0')}`);
            console.log(`[TCP] Response payload: ${response2.payload.toString('hex')}`);
        } else {
            console.log(`[TCP] No second response received`);
        }

    } catch (error) {
        console.error(`[ERROR] ${error.message}`);
    } finally {
        client.close();
    }
}

// Run the script
if (require.main === module) {
    sendFCMRegistration().catch(console.error);
}

module.exports = { SetUserInfoBuilder, DeviceExtBuilder, ShopeeTCPClient };
